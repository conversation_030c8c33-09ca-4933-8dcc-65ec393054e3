const fs = require('fs').promises;
const path = require('path');
const os = require('os');

class ConversationManager {
  /**
   * 构造函数
   */
  constructor() {
    this.conversationHistory = [];
  }

  /**
   * 添加用户消息到对话历史
   * @param {string} message - 用户消息
   */
  addUserMessage(message) {
    // 收集环境信息并添加到用户消息中
    const environmentDetail = this.collectEnvironmentInfo();
    const messageWithEnvironment = `${message}\n\n<environment_detail>\n${environmentDetail}\n</environment_detail>`;
    
    this.conversationHistory.push({
      role: 'user',
      content: messageWithEnvironment
    });
  }

  /**
   * 添加助手消息到对话历史
   * @param {string} message - 助手消息
   */
  addAssistantMessage(message) {
    this.conversationHistory.push({
      role: 'assistant',
      content: message
    });
  }

  /**
   * 添加工具结果到对话历史
   * @param {string} toolName - 工具名称
   * @param {string} result - 工具执行结果
   */
  addToolResult(toolName, result) {
    this.conversationHistory.push({
      role: 'tool',
      name: toolName,
      content: result
    });
  }

  /**
   * 获取完整的对话历史
   * @returns {Array} 对话历史数组
   */
  getConversationHistory() {
    return this.conversationHistory;
  }

  /**
   * 收集环境信息
   * @returns {string} 环境信息字符串
   */
  collectEnvironmentInfo() {
    const envInfo = [];
    
    // 操作系统信息
    envInfo.push(`操作系统: ${os.type()} ${os.release()}`);
    
    // 当前时间
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
    const weekday = weekdays[now.getDay()];
    envInfo.push(`当前时间: ${year}年${month}月${day}日 ${weekday} ${hours}:${minutes}`);
    
    // 终端类型信息
    // 在Windows上获取更准确的终端信息
    let terminalType = '未知';
    if (process.platform === 'win32') {
      // 检查是否是PowerShell
      if (process.env.PATHEXT && process.env.PATHEXT.includes('.PS1')) {
        terminalType = 'PowerShell';
      } else if (process.env.COMSPEC) {
        // 检查是否是cmd
        if (process.env.COMSPEC.includes('cmd.exe')) {
          terminalType = 'Command Prompt';
        } else {
          terminalType = path.basename(process.env.COMSPEC, '.exe');
        }
      }
    } else {
      // Unix-like系统
      terminalType = process.env.SHELL ? path.basename(process.env.SHELL) : '未知';
    }
    envInfo.push(`终端类型: ${terminalType}`);
    
    // 当前目录信息
    const cwd = process.cwd();
    envInfo.push(`当前目录: ${cwd}`);
    
    return envInfo.join('\n');
  }

  /**
   * 构建系统提示词
   * @returns {Promise<string>} 系统提示词
   */
  async buildSystemPrompt() {
    try {
      // 读取系统提示词模板
      const systemPromptPath = path.join(__dirname, '..', 'config', 'system-prompt.txt');
      let systemPrompt = await fs.readFile(systemPromptPath, 'utf8');
      
      // 读取所有工具描述并组合
      const toolsDir = path.join(__dirname, '..', 'config', 'tools');
      const toolFiles = await fs.readdir(toolsDir);
      
      const toolDescriptions = [];
      for (const file of toolFiles) {
        if (file.endsWith('.md')) {
          const toolContent = await fs.readFile(path.join(toolsDir, file), 'utf8');
          toolDescriptions.push(toolContent);
        }
      }
      
      // 替换模板中的占位符
      systemPrompt = systemPrompt.replace('{TOOL_DESCRIPTIONS}', toolDescriptions.join('\n\n'));
      
      return systemPrompt;
    } catch (error) {
      throw new Error(`构建系统提示词失败: ${error.message}`);
    }
  }

  /**
   * 构建完整的对话消息数组
   * @returns {Promise<Array>} 完整的对话消息数组
   */
  async buildMessages() {
    const messages = [];
    
    // 添加系统提示词
    const systemPrompt = await this.buildSystemPrompt();
    messages.push({
      role: 'system',
      content: systemPrompt
    });
    
    // 添加对话历史
    messages.push(...this.conversationHistory);
    
    return messages;
  }

  /**
   * 清除对话历史
   */
  clearHistory() {
    this.conversationHistory = [];
  }
}

module.exports = ConversationManager;
