## update_todo_list
工具描述：Replace the entire TODO list with an updated checklist reflecting the current state. You MUST give full list, Unit test and Documentation MUST be included in the list; the system will overwrite the previous one. This tool is designed for step-by-step task tracking, allowing you to confirm completion of each step before updating, update multiple task statuses at once (e.g., mark one as completed and start the next), and dynamically add new todos discovered during long or complex tasks.

**When to Use:**
- The task involves multiple steps or requires ongoing tracking
- You need to update the status of several todos at once
- New actionable items are discovered during task execution
- The user requests a todo list or provides multiple tasks
- The task is complex and benefits from clear, stepwise progress tracking

**When NOT to Use:**
- There is only a single, trivial task
- The task can be completed in one or two simple steps
- The request is purely conversational or informational

**Core Principles:**
- tasks should mainly about software design, implementation and test
- Before updating, always confirm which todos have been completed since the last update
- You may update multiple statuses in a single update (e.g., mark the previous as completed and the next as in progress)
- When a new actionable item is discovered during a long or complex task, add it to the todo list immediately
- Do not remove any unfinished todos unless explicitly instructed
- Always retain all unfinished tasks, updating their status as needed
- Only mark a task as completed when it is fully accomplished (no partials, no unresolved dependencies)
- If a task is blocked, keep it as in_progress and add a new todo describing what needs to be resolved
- Remove tasks only if they are no longer relevant or if the user requests deletion

```xml
<update_todo_list>
<todos>
[ ] 任务1
[x] 任务2
</todos>
</update_todo_list>
```
示例：
```xml
<update_todo_list>
<todos>
[ ] 完成项目设计
[x] 创建项目结构
[ ] 实现核心功能
</todos>
</update_todo_list>
```
要求：
- 使用标准的markdown待办列表格式
- 可以添加、完成或更新任务
