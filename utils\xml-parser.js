const xml2js = require('xml2js');

class XMLParser {
  constructor() {
    this.parser = new xml2js.Parser({
      explicitArray: false,
      ignoreAttrs: true,
      trim: true
    });
  }

  /**
   * 解析XML字符串
   * @param {string} xmlString - 要解析的XML字符串
   * @returns {Promise<Object>} 解析后的对象
   */
  async parse(xmlString) {
    try {
      const result = await this.parser.parseStringPromise(xmlString);
      return result;
    } catch (error) {
      throw new Error(`XML解析失败: ${error.message}`);
    }
  }

  /**
   * 从XML中提取工具调用信息
   * @param {string} xmlString - 包含工具调用的XML字符串
   * @returns {Promise<Object>} 工具调用信息 { toolName, parameters }
   */
  async extractToolCall(xmlString) {
    try {
      const parsed = await this.parse(xmlString);
      
      // 获取根元素名称作为工具名称
      const toolName = Object.keys(parsed)[0];
      const parameters = parsed[toolName];
      
      return {
        toolName,
        parameters
      };
    } catch (error) {
      throw new Error(`工具调用提取失败: ${error.message}`);
    }
  }
}

module.exports = XMLParser;
