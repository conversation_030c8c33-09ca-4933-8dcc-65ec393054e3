# QAgent - 通用Agent框架

一个基于Node.js的通用Agent框架，可以与OpenAI兼容的API服务交互，并使用各种工具来完成用户任务。

## 最新更新

- API配置现在存储在`config/config.ini`文件中
- 所有工具执行结果现在都包含状态码
- web_fetch工具现在使用puppeteer-core来获取网页内容
- 新增终端用户界面，支持实时待办事项列表显示

## 功能特性

- 与OpenAI兼容的API服务集成
- 模块化工具系统
- 对话历史管理
- 安全的命令执行控制
- XML格式的工具调用解析
- 实时待办事项列表更新

## 项目结构

```
.
├── config/                 # 配置文件
│   ├── tools/             # 工具描述文件
│   ├── command-blacklist.json  # 命令黑名单
│   ├── command-whitelist.json # 命令白名单
│   └── system-prompt.txt      # 系统提示词模板
├── src/                   # 源代码
│   ├── security/           # 安全控制模块
│   ├── agent.js           # Agent核心逻辑
│   ├── conversation-manager.js # 对话管理器
│   ├── openai-client.js   # OpenAI客户端
│   ├── tool-executor.js   # 工具执行器
│   └── index.js          # 主入口文件
├── utils/                 # 工具函数
│   └── xml-parser.js      # XML解析器
├── test-*.js             # 测试文件
├── package.json          # 项目配置
└── README.md             # 项目说明
```

## 安装

1. 克隆项目代码
2. 安装依赖：
   ```bash
   npm install
   ```

## 配置

设置以下环境变量：

- `OPENAI_BASE_URL`: OpenAI兼容API的基础URL（可选，默认为https://api.openai.com）
- `OPENAI_API_KEY`: API密钥（必需）
- `OPENAI_MODEL`: 模型ID（可选，默认为gpt-3.5-turbo）

或者在 `config/config.ini` 文件中设置配置项：

```ini
[API]
base_url = https://api.openai.com
api_key = your-api-key
model = gpt-3.5-turbo

[DEBUG]
enabled = false
```

## 运行

```bash
node src/index.js
```

### 调试模式

要启用调试模式以转储模型输出，可以使用以下方法之一：

1. 使用命令行参数：
   ```bash
   node src/index.js --debug-mode
   # 或者
   node src/index.js -d
   ```

2. 在 `config/config.ini` 文件中设置：
   ```ini
   [DEBUG]
   enabled = true
   ```

当启用调试模式时，模型的原始输出将被打印到控制台，便于调试和分析。

## 使用方法

1. 运行程序后，输入您的问题
2. Agent会使用工具来处理您的请求
3. 待办事项列表会根据Agent的操作动态更新状态
4. 输入"quit"或"exit"退出程序

## 终端用户界面特性

- **实时待办事项列表**：在屏幕顶部显示实时更新的待办事项列表
- **彩色消息显示**：不同角色的消息使用不同颜色区分
  - 系统消息：蓝色
  - 用户消息：青色
  - 助手消息：绿色
  - 工具消息：黄色
- **状态指示**：待办事项使用不同符号表示完成状态
  - ✓ 已完成（绿色）
  - ● 进行中（黄色）
  - ○ 待办（灰色）
- **加载状态**：显示Agent处理请求的加载状态

## 工具说明

框架包含以下默认工具：

- `command_exec`: 执行系统命令（受安全控制）
- `list_dir`: 列出目录内容
- `read_file`: 读取文件内容
- `search_files`: 搜索文件内容
- `ask_question`: 向用户提问
- `web_fetch`: 获取网页内容
- `update_todo_list`: 更新待办列表
- `attempt_completion`: 完成任务并返回结果

## 安全控制

- 命令白名单：自动执行的命令列表
- 命令黑名单：禁止执行的命令列表
- 未在白名单中的命令需要用户确认

## 开发

### 添加新工具

1. 在`config/tools/`目录下创建工具描述文件
2. 在`src/tool-executor.js`中实现工具功能
3. 在`src/conversation-manager.js`中更新系统提示词构建逻辑

### 测试

运行测试脚本：

```bash
node test-environment.js     # 测试环境信息收集
node test-xml-parser.js      # 测试XML解析
node test-tool-executor.js   # 测试工具执行
node test-agent.js           # 测试完整Agent功能
```

## 许可证

MIT
