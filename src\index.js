#!/usr/bin/env node

// 添加全局错误处理
process.on('uncaughtException', (err) => {
  console.error('未捕获的异常:', err);
  // 不要退出进程，继续运行
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝:', reason);
  // 不要退出进程，继续运行
});

const readline = require('readline');
const fs = require('fs');
const path = require('path');
const ini = require('ini');
const Agent = require('./agent');
const TerminalUI = require('./terminal-ui');

// 检查命令行参数中的调试模式
const isDebugMode = process.argv.includes('--debug-mode') || process.argv.includes('-d');

// 创建 readline 接口
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// 读取配置文件
let config = {};
try {
  const configPath = path.join(__dirname, '..', 'config', 'config.ini');
  const configContent = fs.readFileSync(configPath, 'utf-8');
  config = ini.parse(configContent);
} catch (error) {
  console.warn('警告: 无法读取配置文件 config/config.ini，使用默认配置');
  config = {
    API: {
      base_url: 'https://api.openai.com',
      api_key: '',
      model: 'gpt-3.5-turbo'
    },
    DEBUG: {
      enabled: false
    }
  };
}

// 从配置文件或环境变量获取配置
const baseURL = process.env.OPENAI_BASE_URL || config.API.base_url || 'https://api.openai.com';
const apiKey = process.env.OPENAI_API_KEY || config.API.api_key || '';
const model = process.env.OPENAI_MODEL || config.API.model || 'gpt-3.5-turbo';

// 验证必要配置
if (!apiKey) {
  console.error('错误: 请在 config/config.ini 中设置 api_key 或设置 OPENAI_API_KEY 环境变量');
  process.exit(1);
}

// 创建终端用户界面
const ui = new TerminalUI();

// 确定是否启用调试模式（命令行参数优先于配置文件）
const isDebugEnabled = isDebugMode || (config.DEBUG && config.DEBUG.enabled === 'true');

// 创建 Agent 实例
const agent = new Agent(baseURL, apiKey, model, isDebugEnabled, ui, config);

// 显示初始化界面
ui.displayMainUI();

// 初始化 Agent
agent.initialize()
  .then(() => {
    // 显示初始化完成消息
    ui.showSystemMessage('QAgent 初始化完成');
    promptUser();
  })
  .catch((error) => {
    ui.showSystemMessage(`QAgent 初始化失败: ${error.message}`);
    process.exit(1);
  });

// 提示用户输入
function promptUser() {
  rl.question('> ', async (input) => {
    // 检查退出命令
    if (input.toLowerCase() === 'quit' || input.toLowerCase() === 'exit') {
      ui.showSystemMessage('再见!');
      rl.close();
      return;
    }

    // 处理特殊命令
    if (input.trim() === '/help') {
      ui.showHelp();
      promptUser();
      return;
    }
    
    if (input.trim() === '/status') {
      ui.showStatus(config);
      promptUser();
      return;
    }

    // 处理用户输入
    if (input.trim()) {
      ui.showUserMessage(input);
      ui.showLoading('Agent 正在处理您的请求...');
      
      try {
        const result = await agent.processUserInput(input);
        
        // 检查是否需要用户确认执行命令
        let requiresConfirmation = false;
        let commandToConfirm = null;
        
        try {
          const parsedResult = JSON.parse(result);
          if (parsedResult.status === 202 && parsedResult.data && parsedResult.data.requiresConfirmation) {
            requiresConfirmation = true;
            commandToConfirm = parsedResult.data.command;
          }
        } catch (e) {
          // 如果解析失败，忽略错误
        }
        
        // 如果需要用户确认，提示用户确认
        if (requiresConfirmation) {
          ui.showLoading('等待用户确认...');
          const confirmation = await askUserConfirmation(commandToConfirm);
          ui.hideLoading();
          
          if (confirmation) {
            // 用户确认，执行命令
            ui.showSystemMessage(`正在执行命令: ${commandToConfirm}`);
            const { exec } = require('child_process');
            
            // 创建一个新的Promise来处理异步执行并等待其完成
            let commandOutput = '';
            try {
              await new Promise((resolve, reject) => {
                exec(commandToConfirm, { timeout: 30000, encoding: 'buffer' }, (error, stdout, stderr) => {
                  // 处理编码问题
                  if (stdout) {
                    commandOutput = Buffer.isBuffer(stdout) ? stdout.toString('utf8') : stdout;
                  } else if (stderr) {
                    commandOutput = Buffer.isBuffer(stderr) ? stderr.toString('utf8') : stderr;
                  }
                  
                  if (error) {
                    // 处理错误信息的编码
                    const errorMessage = Buffer.isBuffer(error.message) ? error.message.toString('utf8') : error.message;
                    commandOutput = `命令执行失败: ${errorMessage}`;
                  } else {
                    commandOutput = `命令执行成功: ${commandOutput || '命令执行完成，无输出'}`;
                  }
                  resolve();
                });
              });
            } catch (error) {
              commandOutput = `命令执行异常: ${error.message}`;
            }
            
            // 显示命令执行结果
            ui.showSystemMessage(commandOutput);
            
            // 将命令执行结果发送回agent进行进一步处理
            ui.showLoading('Agent 正在处理命令结果...');
            try {
              // 创建一个新的用户输入，模拟用户发送命令结果
              const result = await agent.processUserInput(`命令执行完成，结果如下:\n${commandOutput}`);
              ui.showAssistantMessage(result);
              ui.hideLoading();
            } catch (error) {
              ui.showSystemMessage(`处理命令结果时出错: ${error.message}`);
              ui.hideLoading();
            }
            
            // 继续提示用户输入
            setTimeout(() => {
              try {
                promptUser();
              } catch (e) {
                console.error('Error calling promptUser:', e);
              }
            }, 100);
            return;
          } else {
            // 用户拒绝，显示拒绝消息
            ui.showSystemMessage('用户拒绝执行命令');
            ui.showAssistantMessage(result);
            ui.hideLoading();
            // 继续提示用户输入
            setTimeout(() => {
              try {
                promptUser();
              } catch (e) {
                console.error('Error calling promptUser:', e);
              }
            }, 100);
            return;
          }
        } else {
          // 正常显示结果
          ui.showAssistantMessage(result);
          ui.hideLoading();
          
          // 更新待办事项列表（如果有的话）
          updateTodoListFromResult(result);
        }
        // 继续提示用户输入
        promptUser();
        return;
      } catch (error) {
        ui.showSystemMessage(`处理请求时出错: ${error.message}`);
      }
    }

    // 继续提示用户输入
    promptUser();
  });
}

// 询问用户确认执行命令
function askUserConfirmation(command) {
  return new Promise((resolve) => {
    ui.showSystemMessage(`需要执行命令: ${command}`);
    process.stdout.write('是否确认执行? (y/N): ');
    
    // 监听单个字符输入
    process.stdin.setRawMode(true);
    process.stdin.resume();
    process.stdin.setEncoding('utf8');
    
    const onData = (key) => {
      // 处理 Ctrl+C 退出
      if (key === '\u0003') {
        process.exit();
      }
      
      // 只读取第一个字符
      process.stdin.removeListener('data', onData);
      process.stdin.setRawMode(false);
      process.stdin.pause();
      
      // 用户输入的字符已经在终端中显示，不需要再次输出
      // 只需要添加换行
      process.stdout.write('\n');
      
      const confirmed = key.toLowerCase().startsWith('y');
      resolve(confirmed);
    };
    
    process.stdin.on('data', onData);
  });
}

// 从结果中提取并更新待办事项列表
function updateTodoListFromResult(result) {
  try {
    // 尝试解析JSON结果
    const parsedResult = JSON.parse(result);
    
    // 检查是否是待办事项更新结果
    if (parsedResult.status === 200 && parsedResult.data) {
      // 如果数据是数组，假设它是待办事项列表
      if (Array.isArray(parsedResult.data)) {
        ui.updateTodoList(parsedResult.data);
        return;
      }
      
      // 如果数据有todos属性，使用它作为待办事项列表
      if (parsedResult.data.todos && Array.isArray(parsedResult.data.todos)) {
        ui.updateTodoList(parsedResult.data.todos);
        return;
      }
    }
  } catch (error) {
    // 如果解析失败，忽略错误
  }
  
  // 如果没有找到待办事项更新，刷新当前显示
  ui.refresh();
}
