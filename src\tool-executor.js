const { exec, spawn } = require('child_process');
const fs = require('fs').promises;
const path = require('path');
const util = require('util');
const puppeteer = require('puppeteer-core');
const XMLParser = require('../utils/xml-parser');
const CommandValidator = require('./security/command-validator');

const execPromise = util.promisify(exec);

class ToolExecutor {
  /**
   * 构造函数
   * @param {Object} config - 配置对象
   */
  constructor(config = {}) {
    this.xmlParser = new XMLParser();
    this.commandValidator = new CommandValidator();
    this.config = config;
    this.tools = {
      'command_exec': this.executeCommand.bind(this),
      'list_dir': this.listDirectory.bind(this),
      'read_file': this.readFile.bind(this),
      'search_files': this.searchFiles.bind(this),
      'ask_question': this.askQuestion.bind(this),
      'web_fetch': this.webFetch.bind(this),
      'update_todo_list': this.updateTodoList.bind(this),
      'attempt_completion': this.attemptCompletion.bind(this)
    };
  }

  /**
   * 初始化工具执行器
   * @returns {Promise<void>}
   */
  async initialize() {
    await this.commandValidator.initialize();
  }

  /**
   * 执行工具调用
   * @param {string} toolCallXML - 工具调用的XML字符串
   * @returns {Promise<Object>} 执行结果 { toolName, result }
   */
  async executeTool(toolCallXML) {
    try {
      // 解析工具调用XML
      const toolCall = await this.xmlParser.extractToolCall(toolCallXML);
      const { toolName, parameters } = toolCall;

      // 检查工具是否存在
      if (!this.tools[toolName]) {
        throw new Error(`未知的工具: ${toolName}`);
      }

      // 执行工具
      const result = await this.tools[toolName](parameters);

      return {
        toolName,
        result
      };
    } catch (error) {
      // 检查是否是XML解析错误
      if (error.message.includes('XML解析失败') || error.message.includes('工具调用提取失败')) {
        // XML解析失败时作为未找到工具处理
        return {
          toolName: 'unknown',
          result: JSON.stringify({
            status: 404,
            message: '未找到有效的工具调用',
            data: 'XML解析失败，无法识别工具调用'
          }, null, 2)
        };
      }
      
      // 其他错误正常抛出
      throw new Error(`工具执行失败: ${error.message}`);
    }
  }

  /**
   * 执行命令
   * @param {Object} parameters - 命令参数
   * @returns {Promise<string>} 命令执行结果
   */
  async executeCommand(parameters) {
    const command = parameters.command;
    if (!command) {
      throw new Error('命令参数缺失');
    }

    // 验证命令
    const validation = this.commandValidator.validateCommand(command);
    
    if (!validation.allowed) {
      return JSON.stringify({
        status: 403,
        message: `命令执行被拒绝: ${validation.reason}`,
        data: null
      }, null, 2);
    }

    if (validation.requiresConfirmation) {
      // Return a special status indicating that user confirmation is needed
      return JSON.stringify({
        status: 202,
        message: `命令需要确认才能执行: ${command}`,
        data: {
          command: command,
          requiresConfirmation: true
        }
      }, null, 2);
    }

    return new Promise((resolve) => {
      // Split command into executable and arguments for spawn
      const [executable, ...args] = command.split(' ');
      
      // Spawn the process with stdio inheritance
      const child = spawn(executable, args, {
        stdio: ['inherit', 'pipe', 'pipe'], // stdin inherit, stdout/stderr pipe
        shell: true, // Use shell to handle complex commands
        encoding: 'utf8' // Set encoding to utf8
      });

      let stdout = '';
      let stderr = '';

      // Capture stdout with proper encoding handling
      child.stdout.on('data', (data) => {
        stdout += Buffer.isBuffer(data) ? data.toString('utf8') : data;
      });

      // Capture stderr with proper encoding handling
      child.stderr.on('data', (data) => {
        stderr += Buffer.isBuffer(data) ? data.toString('utf8') : data;
      });
      
      // Handle process completion
      child.on('close', (code) => {
        const output = stdout || stderr;
        if (code === 0) {
          resolve(JSON.stringify({
            status: 200,
            message: '命令执行成功',
            data: output || '命令执行完成，无输出'
          }, null, 2));
        } else {
          resolve(JSON.stringify({
            status: 500,
            message: `命令执行失败，退出码: ${code}`,
            data: output || '命令执行失败'
          }, null, 2));
        }
      });
      
      // Handle process error (e.g., command not found)
      child.on('error', (error) => {
        resolve(JSON.stringify({
          status: 500,
          message: `命令执行失败: ${error.message}`,
          data: null
        }, null, 2));
      });
    });
  }

  /**
   * 列出目录内容
   * @param {Object} parameters - 目录参数
   * @returns {Promise<string>} 目录内容
   */
  async listDirectory(parameters) {
    const dirPath = parameters.path || '.';
    
    try {
      const files = await fs.readdir(dirPath);
      return JSON.stringify({
        status: 200,
        message: '目录列表获取成功',
        data: `目录 ${dirPath} 的内容:\n${files.join('\n')}`
      }, null, 2);
    } catch (error) {
      return JSON.stringify({
        status: 500,
        message: `列出目录失败: ${error.message}`,
        data: null
      }, null, 2);
    }
  }

  /**
   * 读取文件内容
   * @param {Object} parameters - 文件参数
   * @returns {Promise<string>} 文件内容
   */
  async readFile(parameters) {
    const filePath = parameters.path;
    
    if (!filePath) {
      throw new Error('文件路径参数缺失');
    }
    
    try {
      const content = await fs.readFile(filePath, 'utf8');
      return JSON.stringify({
        status: 200,
        message: '文件读取成功',
        data: content
      }, null, 2);
    } catch (error) {
      return JSON.stringify({
        status: 500,
        message: `读取文件失败: ${error.message}`,
        data: null
      }, null, 2);
    }
  }

  /**
   * 搜索文件内容
   * @param {Object} parameters - 搜索参数
   * @returns {Promise<string>} 搜索结果
   */
  async searchFiles(parameters) {
    const dirPath = parameters.path || '.';
    const pattern = parameters.pattern;
    
    if (!pattern) {
      throw new Error('搜索模式参数缺失');
    }
    
    try {
      // 简化实现，实际应该递归搜索目录
      const files = await fs.readdir(dirPath);
      const results = [];
      
      for (const file of files) {
        try {
          const content = await fs.readFile(path.join(dirPath, file), 'utf8');
          if (content.includes(pattern)) {
            results.push(`文件 ${file} 包含匹配内容`);
          }
        } catch (error) {
          // 忽略无法读取的文件
        }
      }
      
      const message = results.length > 0 
        ? `找到匹配的文件:\n${results.join('\n')}` 
        : `未找到匹配的文件`;
        
      return JSON.stringify({
        status: 200,
        message: '文件搜索完成',
        data: message
      }, null, 2);
    } catch (error) {
      return JSON.stringify({
        status: 500,
        message: `搜索文件失败: ${error.message}`,
        data: null
      }, null, 2);
    }
  }

  /**
   * 询问问题
   * @param {Object} parameters - 问题参数
   * @returns {Promise<string>} 问题内容
   */
  async askQuestion(parameters) {
    const question = parameters.question;
    
    if (!question) {
      throw new Error('问题参数缺失');
    }
    
    // 在实际实现中，这里会与用户交互
    return JSON.stringify({
      status: 200,
      message: '问题已提出',
      data: question
    }, null, 2);
  }

  /**
   * 获取网页内容
   * @param {Object} parameters - 网页参数
   * @returns {Promise<string>} 网页内容
   */
  async webFetch(parameters) {
    const url = parameters.url;
    
    if (!url) {
      throw new Error('URL参数缺失');
    }
    
    try {
      // 准备浏览器启动选项
      const launchOptions = {
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox']
      };
      
      // 如果配置了可执行路径且不为空，则使用它
      if (this.config.PUPEPETEER && this.config.PUPEPETEER.executable_path && this.config.PUPEPETEER.executable_path.trim() !== '') {
        launchOptions.executablePath = this.config.PUPEPETEER.executable_path;
      } else {
        // 如果没有指定可执行路径，使用chrome通道
        launchOptions.channel = 'chrome';
      }
      
      // 启动浏览器
      const browser = await puppeteer.launch(launchOptions);
      
      // 打开新页面
      const page = await browser.newPage();
      
      // 设置用户代理
      await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
      
      // 访问网页
      await page.goto(url, { waitUntil: 'networkidle2', timeout: 30000 });
      
      // 等待页面加载完成
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 获取页面标题
      const title = await page.title();
      
      // 提取页面主要内容文本
      const mainContent = await page.evaluate(() => {
        // 移除脚本和样式元素
        const scripts = document.querySelectorAll('script, style');
        scripts.forEach(el => el.remove());
        
        // 获取主要内容
        let content = '';
        
        // 尝试获取主要内容区域
        const mainSelectors = [
          'main',
          'article',
          '.content',
          '#content',
          '.post',
          '.article',
          'body'
        ];
        
        let mainElement = null;
        for (const selector of mainSelectors) {
          mainElement = document.querySelector(selector);
          if (mainElement) {
            break;
          }
        }
        
        // 如果找到了主要内容区域，提取其中的文本
        if (mainElement) {
          // 获取所有文本节点
          const textNodes = [];
          const walker = document.createTreeWalker(
            mainElement,
            NodeFilter.SHOW_TEXT,
            null,
            false
          );
          
          let node;
          while (node = walker.nextNode()) {
            const text = node.textContent.trim();
            if (text.length > 0) {
              textNodes.push(text);
            }
          }
          
          // 合并文本并清理
          content = textNodes.join('\n')
            .replace(/\n\s*\n/g, '\n\n') // 合并多个空行
            .replace(/\s+/g, ' ') // 合并多个空格
            .trim();
        } else {
          // 如果没有找到主要内容区域，使用body的文本
          content = document.body.innerText || document.body.textContent || '';
        }
        
        return content.substring(0, 5000); // 限制内容长度
      });
      
      // 关闭浏览器
      await browser.close();
      
      return JSON.stringify({
        status: 200,
        message: '网页内容获取成功',
        data: {
          url: url,
          title: title,
          content: mainContent
        }
      }, null, 2);
    } catch (error) {
      return JSON.stringify({
        status: 500,
        message: `获取网页内容失败: ${error.message}`,
        data: null
      }, null, 2);
    }
  }

  /**
   * 更新待办列表
   * @param {Object} parameters - 待办列表参数
   * @returns {Promise<string>} 更新结果
   */
  async updateTodoList(parameters) {
    const todos = parameters.todos;
    
    if (!todos) {
      throw new Error('待办列表参数缺失');
    }
    
    // 在实际实现中，这里会更新待办列表状态
    return JSON.stringify({
      status: 200,
      message: '待办列表已更新',
      data: todos
    }, null, 2);
  }

  /**
   * 完成任务
   * @param {Object} parameters - 完成参数
   * @returns {Promise<string>} 完成结果
   */
  async attemptCompletion(parameters) {
    const result = parameters.result;
    
    if (!result) {
      throw new Error('结果参数缺失');
    }
    
    // 在实际实现中，这里会结束对话
    return JSON.stringify({
      status: 200,
      message: '任务已完成',
      data: `任务完成: ${result}`
    }, null, 2);
  }
}

module.exports = ToolExecutor;
