{"name": "general-agent-framework", "version": "1.0.0", "description": "A general-purpose agent framework with tool execution capabilities", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "node src/index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["agent", "ai", "openai", "tool-execution"], "author": "General Agent <PERSON><PERSON><PERSON>", "license": "MIT", "dependencies": {"axios": "^1.11.0", "blessed": "^0.1.81", "chalk": "^5.5.0", "ini": "^5.0.0", "puppeteer-core": "^24.16.1", "xml2js": "^0.6.2"}, "devDependencies": {"nodemon": "^3.0.1"}}