const fs = require('fs').promises;
const path = require('path');

class CommandValidator {
  /**
   * 构造函数
   */
  constructor() {
    this.whitelist = null;
    this.blacklist = null;
    this.initialized = false;
  }

  /**
   * 初始化命令验证器
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      // 读取白名单配置
      const whitelistPath = path.join(__dirname, '..', '..', 'config', 'command-whitelist.json');
      const whitelistContent = await fs.readFile(whitelistPath, 'utf8');
      this.whitelist = JSON.parse(whitelistContent);

      // 读取黑名单配置
      const blacklistPath = path.join(__dirname, '..', '..', 'config', 'command-blacklist.json');
      const blacklistContent = await fs.readFile(blacklistPath, 'utf8');
      this.blacklist = JSON.parse(blacklistContent);

      this.initialized = true;
    } catch (error) {
      throw new Error(`命令验证器初始化失败: ${error.message}`);
    }
  }

  /**
   * 验证命令是否可以执行
   * @param {string} command - 要验证的命令
   * @returns {Object} 验证结果 { allowed: boolean, reason: string, requiresConfirmation: boolean }
   */
  validateCommand(command) {
    if (!this.initialized) {
      throw new Error('命令验证器未初始化');
    }

    // 检查黑名单
    if (this.isBlacklisted(command)) {
      return {
        allowed: false,
        reason: '命令在黑名单中，禁止执行',
        requiresConfirmation: false
      };
    }

    // 检查白名单
    if (this.isWhitelisted(command)) {
      return {
        allowed: true,
        reason: '命令在白名单中，自动执行',
        requiresConfirmation: false
      };
    }

    // 不在白名单也不在黑名单，需要用户确认
    return {
      allowed: true,
      reason: '命令需要用户确认后执行',
      requiresConfirmation: true
    };
  }

  /**
   * 检查命令是否在黑名单中
   * @param {string} command - 要检查的命令
   * @returns {boolean} 是否在黑名单中
   */
  isBlacklisted(command) {
    // 检查精确匹配
    if (this.blacklist.commands.includes(command)) {
      return true;
    }

    // 检查模式匹配
    for (const pattern of this.blacklist.commandPatterns) {
      const regex = new RegExp(pattern);
      if (regex.test(command)) {
        return true;
      }
    }

    return false;
  }

  /**
   * 检查命令是否在白名单中
   * @param {string} command - 要检查的命令
   * @returns {boolean} 是否在白名单中
   */
  isWhitelisted(command) {
    // 检查精确匹配
    if (this.whitelist.commands.includes(command)) {
      return true;
    }

    // 检查模式匹配
    for (const pattern of this.whitelist.commandPatterns) {
      const regex = new RegExp(pattern);
      if (regex.test(command)) {
        return true;
      }
    }

    return false;
  }
}

module.exports = CommandValidator;
