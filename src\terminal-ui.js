const chalk = require('chalk').default;
const path = require('path');
const os = require('os');

class TerminalUI {
  constructor() {
    this.todos = [];
    this.cwd = process.cwd();
    this.bannerDisplayed = false;
  }

  /**
   * 显示QAgent主界面
   */
  displayMainUI() {
    // 只在第一次显示Banner和Tips
    if (!this.bannerDisplayed) {
      // 显示带框的Banner
      console.log(chalk.gray('┌────────────────────────────────────────────────────────────┐'));
      console.log(chalk.gray('│') + chalk.gray('  Welcome to ') + chalk.blue('QAX ') + chalk.blue.bold('QAgent') + chalk.gray('                                     │'));
      console.log(chalk.gray('│') + chalk.gray('  /help for help, /status for your current setup            │'));
      console.log(chalk.gray('│') + chalk.gray(`  Cwd: ${this.cwd}`) + chalk.gray('                                 │'));
      console.log(chalk.gray('└────────────────────────────────────────────────────────────┘'));
      console.log();
      
      // 显示Tips
      console.log(chalk.yellow('Tips:'));
      console.log(chalk.gray('• 输入您的问题，QAgent将使用工具来帮助您解决问题'));
      console.log(chalk.gray('• 输入 /help 查看帮助信息'));
      console.log(chalk.gray('• 输入 /status 查看当前配置'));
      console.log(chalk.gray('• 输入 /quit 或 /exit 退出程序'));
      console.log();
      
      this.bannerDisplayed = true;
    }
  }

  /**
   * 更新待办事项列表
   * @param {Array} todos - 待办事项数组
   */
  updateTodoList(todos) {
    this.todos = todos;
    // 不再显示主界面，只更新待办事项
  }

  /**
   * 显示系统消息
   * @param {string} message - 消息内容
   */
  showSystemMessage(message) {
    console.log(chalk.blue(`[系统] ${message}`));
  }

  /**
   * 显示用户消息
   * @param {string} message - 消息内容
   */
  showUserMessage(message) {
    console.log(chalk.cyan(`[用户] ${message}`));
  }

  /**
   * 显示助手消息
   * @param {string} message - 消息内容
   */
  showAssistantMessage(message) {
    // 检查消息是否为JSON格式
    try {
      const parsed = JSON.parse(message);
      
      // 如果是工具调用结果，直接显示数据部分
      if (parsed.status && parsed.data) {
        console.log(chalk.green('[助手]'));
        
        // 直接显示数据内容
        if (typeof parsed.data === 'string') {
          console.log(parsed.data);
        } else if (typeof parsed.data === 'object') {
          // 如果是对象，格式化显示
          if (Array.isArray(parsed.data)) {
            parsed.data.forEach((item, index) => {
              console.log(`${index + 1}. ${typeof item === 'object' ? JSON.stringify(item, null, 2) : item}`);
            });
          } else {
            // 对于对象类型，直接显示其内容
            // 如果有message字段，优先显示
            if (parsed.data.message) {
              console.log(parsed.data.message);
            } else {
              // 否则格式化显示整个对象
              console.log(JSON.stringify(parsed.data, null, 2));
            }
          }
        }
        return;
      }
      
      // 如果是其他JSON格式，直接显示数据部分
      console.log(chalk.green('[助手]'));
      console.log(JSON.stringify(parsed, null, 2));
    } catch (e) {
      // 如果不是JSON格式，直接显示
      console.log(chalk.green(`[助手] ${message}`));
    }
  }

  /**
   * 显示工具消息
   * @param {string} message - 消息内容
   */
  showToolMessage(message) {
    console.log(chalk.yellow(`[工具] ${message}`));
  }

  /**
   * 显示工具执行信息
   * @param {string} toolName - 工具名称
   * @param {boolean} success - 是否成功
   * @param {string} message - 执行消息
   */
  showToolExecutionInfo(toolName, success, message) {
    const status = success ? '成功' : '失败';
    const color = success ? chalk.green : chalk.red;
    console.log(color(`[工具执行] ${toolName} - ${status}: ${message}`));
  }

  /**
   * 显示加载状态
   * @param {string} message - 加载消息
   */
  showLoading(message) {
    console.log(chalk.yellow(`⏳ ${message}`));
  }

  /**
   * 隐藏加载状态
   */
  hideLoading() {
    // 在这个简单的实现中，我们不需要做任何事情
    // 在更复杂的实现中，可能会清除加载指示器
  }

  /**
   * 显示帮助信息
   */
  showHelp() {
    console.log(chalk.yellow('帮助信息:'));
    console.log(chalk.gray('• 输入您的问题，QAgent将使用工具来帮助您解决问题'));
    console.log(chalk.gray('• 输入 /help 查看帮助信息'));
    console.log(chalk.gray('• 输入 /status 查看当前配置'));
    console.log(chalk.gray('• 输入 /quit 或 /exit 退出程序'));
    console.log();
  }

  /**
   * 显示当前配置状态
   * @param {Object} config - 当前配置
   */
  showStatus(config) {
    console.log(chalk.yellow('当前配置:'));
    console.log(chalk.gray(`• API地址: ${config.API.base_url}`));
    console.log(chalk.gray(`• 模型: ${config.API.model}`));
    console.log(chalk.gray(`• 调试模式: ${config.DEBUG.enabled === 'true' ? '启用' : '禁用'}`));
    console.log();
  }

  /**
   * 清除屏幕并重新显示主界面
   */
  refresh() {
    // 不再刷新主界面
  }
}

module.exports = TerminalUI;
