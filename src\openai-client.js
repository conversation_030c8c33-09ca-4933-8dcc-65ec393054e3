const axios = require('axios');

class OpenAIClient {
  /**
   * 构造函数
   * @param {string} baseURL - API基础URL
   * @param {string} apiKey - API密钥
   * @param {string} model - 模型ID
   * @param {boolean} isDebugMode - 是否启用调试模式
   */
  constructor(baseURL, apiKey, model, isDebugMode = false) {
    this.baseURL = baseURL;
    this.apiKey = apiKey;
    this.model = model;
    this.isDebugMode = isDebugMode;
    this.client = axios.create({
      baseURL: this.baseURL,
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json'
      }
    });
  }

  /**
   * 调用聊天完成API
   * @param {Array} messages - 对话历史消息
   * @returns {Promise<string>} 模型回复
   */
  async chatCompletion(messages) {
    try {
      const response = await this.client.post('/v1/chat/completions', {
        model: this.model,
        messages: messages,
        temperature: 0.7,
        max_tokens: 2048
      });

      // 如果启用调试模式，输出模型的原始响应
      if (this.isDebugMode) {
        console.log('\n=== DEBUG: Model Output ===');
        console.log(response.data.choices[0].message.content);
        console.log('=== END DEBUG ===\n');
      }

      return response.data.choices[0].message.content;
    } catch (error) {
      if (error.response) {
        throw new Error(`API调用失败: ${error.response.status} - ${error.response.data.error?.message || error.message}`);
      } else {
        throw new Error(`API调用失败: ${error.message}`);
      }
    }
  }
}

module.exports = OpenAIClient;
