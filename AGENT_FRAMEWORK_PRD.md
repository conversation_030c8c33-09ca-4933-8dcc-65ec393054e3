# QAgent - 通用Agent框架产品需求文档 (PRD)

## 1. 概述

### 1.1 项目目标
开发一个基于Node.js的通用命令行Agent框架，能够与OpenAI兼容的API服务进行交互，支持模块化工具扩展，并具备安全的命令执行机制。

### 1.2 核心价值
- 提供统一的Agent框架，便于快速开发各类智能助手应用
- 支持与多种OpenAI兼容的API服务集成
- 通过模块化工具设计，实现功能的灵活扩展
- 通过安全机制确保命令执行的安全性

## 2. 功能需求

### 2.1 核心功能

#### 2.1.1 API集成
- 2.1.1.1 支持OpenAI兼容的API服务
- 2.1.1.2 可配置API base URL
- 2.1.1.3 可配置model ID
- 2.1.1.4 支持API密钥认证

#### 2.1.2 对话管理
- 2.1.2.1 维护全局对话历史记录
- 2.1.2.2 构建完整的对话上下文
- 2.1.2.3 支持多轮对话交互
- 2.1.2.4 自动收集环境信息并添加到用户输入

#### 2.1.3 系统提示词管理
- 2.1.3.1 从文本文件读取系统提示词模板
- 2.1.3.2 支持模块化工具描述集成
- 2.1.3.3 每个工具包含描述、示例和要求三个部分

#### 2.1.4 工具执行引擎
- 2.1.4.1 解析模型输出的工具调用
- 2.1.4.2 执行相应的工具功能
- 2.1.4.3 返回工具执行结果
- 2.1.4.4 限制每次只能执行一个工具

### 2.2 工具系统

#### 2.2.1 默认工具集
- 2.2.1.1 command_exec: 安全命令执行工具
- 2.2.1.2 list_dir: 目录列表工具
- 2.2.1.3 read_file: 文件读取工具
- 2.2.1.4 search_files: 文件搜索工具
- 2.2.1.5 ask_question: 问题询问工具
- 2.2.1.6 web_fetch: 网页获取工具
- 2.2.1.7 update_todo_list: 待办列表更新工具
- 2.2.1.8 attempt_completion: 任务完成工具

#### 2.2.2 工具描述规范
- 2.2.2.1 每个工具包含工具描述部分
- 2.2.2.2 每个工具包含使用示例部分
- 2.2.2.3 每个工具包含使用要求部分
- 2.2.2.4 工具描述以标准化格式存储

### 2.3 安全控制

#### 2.3.1 命令执行安全
- 2.3.1.1 自动执行命令白名单
- 2.3.1.2 禁止执行命令黑名单
- 2.3.1.3 用户确认执行机制
- 2.3.1.4 命令执行日志记录

#### 2.3.2 工具调用安全
- 2.3.2.1 工具调用格式验证
- 2.3.2.2 工具参数合法性检查
- 2.3.2.3 工具执行结果安全处理

### 2.4 环境信息收集

#### 2.4.1 系统信息
- 2.4.1.1 操作系统类型和版本
- 2.4.1.2 当前shell类型
- 2.4.1.3 工作目录信息

#### 2.4.2 文件系统信息
- 2.4.2.1 当前工作目录文件列表
- 2.4.2.2 目录结构信息
- 2.4.2.3 文件权限信息

## 3. 非功能需求

### 3.1 性能要求
- 3.1.1 工具执行响应时间不超过5秒
- 3.1.2 对话历史管理内存占用合理
- 3.1.3 支持长时间运行会话

### 3.2 可靠性要求
- 3.2.1 API调用失败重试机制
- 3.2.2 工具执行异常处理
- 3.2.3 系统崩溃恢复能力

### 3.3 可扩展性要求
- 3.3.1 工具模块化设计
- 3.3.2 配置文件可扩展
- 3.3.3 插件化架构支持

### 3.4 安全性要求
- 3.4.1 命令执行权限控制
- 3.4.2 敏感信息保护
- 3.4.3 输入验证和过滤

## 4. 技术架构

### 4.1 技术栈
- 4.1.1 运行环境: Node.js
- 4.1.2 包管理: npm
- 4.1.3 编程语言: JavaScript
- 4.1.4 命令行界面: 标准输入输出

### 4.2 架构设计
- 4.2.1 分层架构设计
- 4.2.2 模块化组件设计
- 4.2.3 依赖注入设计模式
- 4.2.4 事件驱动架构

### 4.3 数据流设计
- 4.3.1 用户输入处理流程
- 4.3.2 API调用处理流程
- 4.3.3 工具执行处理流程
- 4.3.4 结果返回处理流程

## 5. 详细设计

### 5.1 目录结构
```
d:/code/generalAgent/
├── package.json              # 项目依赖配置
├── config/                    
│   ├── system-prompt.txt      # 系统提示词模板
│   ├── command-whitelist.json  # 自动执行命令白名单
│   ├── command-blacklist.json  # 禁止执行命令黑名单
│   └── tools/                # 工具描述文件
├── src/
│   ├── index.js               # 主入口文件
│   ├── agent.js               # Agent核心逻辑
│   ├── openai-client.js       # OpenAI API客户端
│   ├── tool-executor.js       # 工具执行器
│   ├── conversation-manager.js # 对话管理器
│   ├── security/              # 安全控制模块
│   └── tools/                # 工具实现
└── utils/
    └── xml-parser.js          # XML解析工具
```

### 5.2 核心模块设计

#### 5.2.1 Agent核心模块
- 5.2.1.1 初始化系统配置
- 5.2.1.2 管理对话流程
- 5.2.1.3 协调各组件工作

#### 5.2.2 OpenAI客户端模块
- 5.2.2.1 API连接管理
- 5.2.2.2 请求构建和发送
- 5.2.2.3 响应解析和处理

#### 5.2.3 对话管理模块
- 5.2.3.1 对话历史维护
- 5.2.3.2 Prompt构建
- 5.2.3.3 环境信息收集

#### 5.2.4 工具执行模块
- 5.2.4.1 工具调用解析
- 5.2.4.2 工具执行调度
- 5.2.4.3 结果处理和返回

#### 5.2.5 安全控制模块
- 5.2.5.1 命令验证
- 5.2.5.2 权限检查
- 5.2.5.3 用户确认处理

## 6. 工作流程

### 6.1 系统启动流程
1. 读取配置文件
2. 初始化各功能模块
3. 启动命令行交互界面
4. 等待用户输入

### 6.2 对话处理流程
1. 接收用户输入
2. 收集环境信息
3. 构建完整对话历史
4. 调用模型API
5. 解析模型响应
6. 执行工具调用
7. 返回工具结果
8. 继续下一轮对话

### 6.3 工具执行流程
1. 解析工具调用XML
2. 验证工具名称和参数
3. 执行相应工具功能
4. 返回执行结果
5. 记录执行日志

## 7. 配置管理

### 7.1 系统配置
- 7.1.1 API配置（base URL, model ID）
- 7.1.2 安全配置（白名单、黑名单）
- 7.1.3 日志配置
- 7.1.4 调试配置

### 7.2 工具配置
- 7.2.1 工具描述文件
- 7.2.2 工具参数定义
- 7.2.3 工具依赖关系

## 8. 测试策略

### 8.1 单元测试
- 8.1.1 核心模块单元测试
- 8.1.2 工具模块单元测试
- 8.1.3 安全模块单元测试

### 8.2 集成测试
- 8.2.1 API调用集成测试
- 8.2.2 工具执行集成测试
- 8.2.3 对话流程集成测试

### 8.3 安全测试
- 8.3.1 命令执行安全测试
- 8.3.2 输入验证安全测试
- 8.3.3 权限控制安全测试
