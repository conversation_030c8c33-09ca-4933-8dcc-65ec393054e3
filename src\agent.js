const OpenAIClient = require('./openai-client');
const ConversationManager = require('./conversation-manager');
const ToolExecutor = require('./tool-executor');

class Agent {
  /**
   * 构造函数
   * @param {string} baseURL - API基础URL
   * @param {string} apiKey - API密钥
   * @param {string} model - 模型ID
   * @param {boolean} isDebugMode - 是否启用调试模式
   * @param {TerminalUI} ui - 终端用户界面实例
   * @param {Object} config - 配置对象
   */
  constructor(baseURL, apiKey, model, isDebugMode = false, ui = null, config = {}) {
    this.openaiClient = new OpenAIClient(baseURL, apiKey, model, isDebugMode);
    this.conversationManager = new ConversationManager();
    this.toolExecutor = new ToolExecutor(config);
    this.initialized = false;
    this.isDebugMode = isDebugMode;
    this.ui = ui;
    this.config = config;
  }

  /**
   * 初始化Agent
   * @returns {Promise<void>}
   */
  async initialize() {
    await this.toolExecutor.initialize();
    this.initialized = true;
  }

  /**
   * 处理用户输入
   * @param {string} userInput - 用户输入
   * @returns {Promise<string>} 处理结果
   */
  async processUserInput(userInput) {
    if (!this.initialized) {
      throw new Error('Agent未初始化');
    }

    try {
      // 添加用户消息到对话历史
      this.conversationManager.addUserMessage(userInput);

      // 进行对话循环直到任务完成
      let isTaskCompleted = false;
      let result = '';
      let retryCount = 0;
      const maxRetries = 3; // 最大重试次数

      while (!isTaskCompleted && retryCount < maxRetries) {
        // 构建完整的对话消息
        const messages = await this.conversationManager.buildMessages();

        // 调用模型API
        const modelResponse = await this.openaiClient.chatCompletion(messages);

        // 添加助手消息到对话历史
        this.conversationManager.addAssistantMessage(modelResponse);

        // 检查模型响应是否包含工具调用
        if (this.containsToolCall(modelResponse)) {
          // 提取工具调用XML
          const toolCallXML = this.extractToolCallXML(modelResponse);

          if (toolCallXML) {
          // 执行工具调用
          const toolResult = await this.toolExecutor.executeTool(toolCallXML);

          // 检查是否是命令执行工具且需要用户确认
          let requiresConfirmation = false;
          let commandToConfirm = null;
          
          if (toolResult.toolName === 'command_exec') {
            try {
              const parsedResult = JSON.parse(toolResult.result);
              if (parsedResult.status === 202 && parsedResult.data && parsedResult.data.requiresConfirmation) {
                requiresConfirmation = true;
                commandToConfirm = parsedResult.data.command;
              }
            } catch (e) {
              // 如果解析失败，忽略错误
            }
          }

          // 如果需要用户确认，返回特殊结果
          if (requiresConfirmation) {
            return JSON.stringify({
              status: 202,
              message: '需要用户确认执行命令',
              data: {
                command: commandToConfirm,
                requiresConfirmation: true
              }
            }, null, 2);
          }

          // 显示工具执行信息
          if (this.ui) {
            try {
              const parsedResult = JSON.parse(toolResult.result);
              const success = parsedResult.status >= 200 && parsedResult.status < 300;
              
              // 特殊处理ask_question工具
              if (toolResult.toolName === 'ask_question') {
                // 显示问题
                if (parsedResult.data) {
                  this.ui.showUserMessage(parsedResult.data);
                }
                // 显示工具执行信息
                const message = parsedResult.message || '工具执行完成';
                this.ui.showToolExecutionInfo(toolResult.toolName, success, message);
              } else {
                // 其他工具的默认处理
                const message = parsedResult.message || '工具执行完成';
                this.ui.showToolExecutionInfo(toolResult.toolName, success, message);
              }
            } catch (e) {
              // 如果解析失败，显示原始结果
              this.ui.showToolExecutionInfo(toolResult.toolName, true, '工具执行完成');
            }
          }

          // 添加工具结果到对话历史
          this.conversationManager.addToolResult(toolResult.toolName, toolResult.result);

          // 检查是否是完成任务的调用
          if (toolResult.toolName === 'ask_question') {
            isTaskCompleted = true;
            result = toolResult.result;
          } else if (toolResult.toolName === 'attempt_completion') {
            // 对于attempt_completion，不结束任务，继续对话循环
            result = toolResult.result;
          } else if (toolResult.toolName === 'update_todo_list') {
            // 对于待办事项更新，继续对话循环
            // 结果将在下一次迭代中处理
            result = toolResult.result;
          } else if (toolResult.toolName === 'unknown') {
            // XML解析失败，作为未找到工具处理
            // 将错误信息发回给模型进行重试
            this.conversationManager.addToolResult('unknown', toolResult.result);
            retryCount++;
          }
          } else {
            // 没有找到有效的工具调用
            // 将错误信息发回给模型进行重试
            const errorMessage = '错误：模型响应中没有有效的工具调用';
            this.conversationManager.addToolResult('unknown', JSON.stringify({
              status: 400,
              message: errorMessage,
              data: modelResponse
            }, null, 2));
            retryCount++;
          }
        } else {
          // 没有工具调用，可能是错误情况
          // 将错误信息发回给模型进行重试
          const errorMessage = '错误：模型响应中必须包含工具调用';
          this.conversationManager.addToolResult('unknown', JSON.stringify({
            status: 400,
            message: errorMessage,
            data: modelResponse
          }, null, 2));
          retryCount++;
        }
      }

      // 如果达到最大重试次数仍未完成任务，返回错误信息
      if (retryCount >= maxRetries && !isTaskCompleted) {
        result = '错误：模型未能生成有效的工具调用，已达到最大重试次数';
      }

      return result;
    } catch (error) {
      throw new Error(`处理用户输入失败: ${error.message}`);
    }
  }

  /**
   * 检查响应是否包含工具调用
   * @param {string} response - 模型响应
   * @returns {boolean} 是否包含工具调用
   */
  containsToolCall(response) {
    // 简单检查是否包含XML标签
    return response.includes('<') && response.includes('>');
  }

  /**
   * 提取工具调用XML
   * @param {string} response - 模型响应
   * @returns {string|null} 工具调用XML或null
   */
  extractToolCallXML(response) {
    // 提取完整的XML块，包括嵌套标签
    const xmlMatch = response.match(/<(\w+)[^>]*>[\s\S]*?<\/\1>/);
    return xmlMatch ? xmlMatch[0] : null;
  }

  /**
   * 清除对话历史
   */
  clearHistory() {
    this.conversationManager.clearHistory();
  }
}

module.exports = Agent;
