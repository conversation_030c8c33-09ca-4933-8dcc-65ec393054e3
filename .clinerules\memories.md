# Security
- 执行command_exec时，应区分自动执行和需用户确认的命令列表以增强安全性

# DevelopmentProcess
- 用户要求严格按照PRD进行开发，并提供明确的任务列表以供执行
- 用户要求清理测试程序，属于开发流程中环境维护的一部分。

# ImplementationDetails
- 工具执行时应输出工具名称及执行状态（成功/失败），使用状态码表示结果；当模型未返回有效工具调用时，应将错误信息作为结果发给模型进行重试。
- XML解析失败时应作为未找到工具处理，即使XML格式正确也需避免抛出异常导致程序退出
- 执行ask_question工具后应显示所提出的问题并返回用户输入提示状态
- 使用 puppeteer-core 时需要配置 executablePath 或 channel 参数以指定浏览器可执行文件路径
- 工具执行完成后不应直接返回，而应继续循环处理或回到用户提示状态，确保流程不中断
- web_fetch 工具需具备网页渲染能力，能够获取渲染后页面的主要内容。

# CLI Design
- 命令行界面启动时应显示简洁的文本Banner，内容为'Welcome to QAX QAgent'，避免使用复杂的ASCII艺术图案，并确保边框左右对齐，右侧需按照最长行进行对齐。
- 需要实现/help和/status两个命令行指令，用于查看帮助信息和当前配置状态，未提供具体要求时应提示用户输入
- 用户要求在命令行交互过程中保留历史对话内容并保持banner在屏幕顶部，内容超出时应滚动显示而非清屏，以确保上下文连续性和界面稳定性。
- 命令执行结果应直接输出内容，不包含'状态: 200'、'结果:'或'任务完成:'等固定前缀，[助手]后直接回复核心内容即可
- 命令行交互时，确认提示后光标应停在原行不换行，且命令执行后应输出结果并返回用户提示而非直接退出

# FrontendImplementation
- 用户遇到UI方法调用错误，表明在前端实现中调用了不存在的hideLoading函数。

# Deployment
- 用户希望将程序封装为可执行的exe文件，表明对应用打包和分发有需求。
