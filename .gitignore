# Dependencies
node_modules/

# Configuration files (contains sensitive information)
config/config.ini

# Logs
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# Distribution / packaging
*.tgz
*.gz

# Debug log files
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment files
.env*

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
tmp/
temp/

# Build outputs
dist/
build/

# Test coverage
.nyc_output/
